import { useState, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { ArrowLeft, RefreshCw } from 'lucide-react'

export default function BCELogin() {
  const [formData, setFormData] = useState({
    employee_id: '',
    password: '',
    captcha_answer: ''
  })
  const [captcha, setCaptcha] = useState({ question: '', loading: false })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const navigate = useNavigate()

  useEffect(() => {
    generateCaptcha()
  }, [])

  const generateCaptcha = async () => {
    setCaptcha({ question: '', loading: true })
    try {
      const response = await fetch('http://localhost:8000/api/auth/generate-captcha/', {
        method: 'POST',
        credentials: 'include',
      })

      const data = await response.json()
      if (data.success) {
        setCaptcha({ question: data.question, loading: false })
      } else {
        setError('Failed to generate captcha')
        setCaptcha({ question: '', loading: false })
      }
    } catch (error) {
      setError('Failed to generate captcha')
      setCaptcha({ question: '', loading: false })
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const response = await fetch('http://localhost:8000/api/auth/bce-login/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (data.success) {
        navigate('/dashboard')
      } else {
        setError(data.error || 'Login failed')
        generateCaptcha() // Generate new captcha on failed attempt
        setFormData({ ...formData, captcha_answer: '' })
      }
    } catch (error) {
      setError('Network error. Please try again.')
      generateCaptcha()
      setFormData({ ...formData, captcha_answer: '' })
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-background">
      <div className="w-full max-w-md space-y-8 px-4">
        <div className="text-center space-y-4">
          <Link
            to="/"
            className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Home
          </Link>

          <div className="space-y-2">
            <h1 className="text-3xl font-bold text-foreground">
              BCE Internal Login
            </h1>
            <p className="text-muted-foreground">
              Access the internal talent management system
            </p>
          </div>
        </div>

        <div className="bg-card rounded-lg border border-border p-8 shadow-sm">
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="bg-destructive/10 border border-destructive/20 rounded-md p-3">
                <p className="text-sm text-destructive">{error}</p>
              </div>
            )}

            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-card-foreground">
                  Employee ID
                </label>
                <input
                  type="text"
                  name="employee_id"
                  value={formData.employee_id}
                  onChange={handleInputChange}
                  required
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  placeholder="Enter your employee ID"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-card-foreground">
                  Password
                </label>
                <input
                  type="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  placeholder="Enter your password"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-card-foreground">
                  Security Question
                </label>
                <div className="flex items-center space-x-2">
                  <div className="flex-1 p-3 bg-muted rounded-md border">
                    <span className="text-sm font-mono">
                      {captcha.loading ? 'Loading...' : captcha.question || 'Click refresh to generate'}
                    </span>
                  </div>
                  <button
                    type="button"
                    onClick={generateCaptcha}
                    disabled={captcha.loading}
                    className="p-2 rounded-md border border-input hover:bg-muted transition-colors disabled:opacity-50"
                  >
                    <RefreshCw className={`h-4 w-4 ${captcha.loading ? 'animate-spin' : ''}`} />
                  </button>
                </div>
                <input
                  type="number"
                  name="captcha_answer"
                  value={formData.captcha_answer}
                  onChange={handleInputChange}
                  required
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  placeholder="Enter the answer"
                />
              </div>

              <button
                type="submit"
                disabled={loading || !captcha.question}
                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-secondary text-secondary-foreground hover:bg-secondary/90 h-10 px-4 py-2 w-full"
              >
                {loading ? 'Signing In...' : 'Sign In'}
              </button>
            </div>
          </form>
        </div>

        <div className="text-center text-sm text-muted-foreground">
          <p>Test Credentials:</p>
          <p>Superadmin: SA001 / SuperAdmin@123</p>
          <p>Admin: AD001 / Admin@123</p>
          <p>User: US001 / User@123</p>
        </div>
      </div>
    </div>
  )
}
