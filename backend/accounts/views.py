from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt, ensure_csrf_cookie
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View
from django.middleware.csrf import get_token
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
import json
import random
import logging

logger = logging.getLogger(__name__)

class ArithmeticCaptcha:
    """Simple arithmetic captcha generator"""

    @staticmethod
    def generate():
        """Generate a simple arithmetic problem"""
        num1 = random.randint(1, 20)
        num2 = random.randint(1, 20)
        operation = random.choice(['+', '-', '*'])

        if operation == '+':
            answer = num1 + num2
        elif operation == '-':
            answer = num1 - num2
        else:  # multiplication
            answer = num1 * num2

        question = f"{num1} {operation} {num2}"
        return question, answer

@api_view(['POST'])
@permission_classes([AllowAny])
@csrf_exempt
def generate_captcha(request):
    """Generate arithmetic captcha for login"""
    try:
        question, answer = ArithmeticCaptcha.generate()

        # Ensure session exists
        if not request.session.session_key:
            request.session.create()

        # Store answer in session with timestamp
        import time
        request.session['captcha_answer'] = answer
        request.session['captcha_generated'] = True
        request.session['captcha_timestamp'] = time.time()
        request.session.save()

        logger.info(f"Captcha generated: {question} = {answer} (Session: {request.session.session_key})")

        return Response({
            'success': True,
            'question': question
        })
    except Exception as e:
        logger.error(f"Error generating captcha: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to generate captcha'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([AllowAny])
@csrf_exempt
def vendor_login(request):
    """Vendor login with arithmetic captcha"""
    try:
        data = request.data
        email = data.get('email')
        password = data.get('password')
        captcha_answer = data.get('captcha_answer')

        # Validate required fields
        if not all([email, password, captcha_answer]):
            return Response({
                'success': False,
                'error': 'Email, password, and captcha answer are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate captcha
        session_answer = request.session.get('captcha_answer')
        if not session_answer:
            return Response({
                'success': False,
                'error': 'Captcha expired. Please refresh and try again.'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            if int(captcha_answer) != session_answer:
                return Response({
                    'success': False,
                    'error': 'Invalid captcha answer'
                }, status=status.HTTP_400_BAD_REQUEST)
        except (ValueError, TypeError):
            return Response({
                'success': False,
                'error': 'Invalid captcha format'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Authenticate user
        user = authenticate(request, username=email, password=password)
        if user and user.is_active and user.user_type == 'vendor':
            login(request, user)

            # Clear captcha from session
            request.session.pop('captcha_answer', None)
            request.session.pop('captcha_generated', None)

            # Log successful login
            logger.info(f"Vendor login successful: {email}")

            return Response({
                'success': True,
                'user': {
                    'id': str(user.id),
                    'email': user.email,
                    'full_name': user.full_name,
                    'user_type': user.user_type,
                }
            })
        else:
            logger.warning(f"Failed vendor login attempt: {email}")
            return Response({
                'success': False,
                'error': 'Invalid credentials or account not authorized for vendor access'
            }, status=status.HTTP_401_UNAUTHORIZED)

    except Exception as e:
        logger.error(f"Error in vendor login: {str(e)}")
        return Response({
            'success': False,
            'error': 'Login failed'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([AllowAny])
@csrf_exempt
def bce_login(request):
    """BCE internal login with arithmetic captcha"""
    try:
        data = request.data
        email = data.get('email')
        password = data.get('password')
        captcha_answer = data.get('captcha_answer')

        # Validate required fields
        if not all([email, password, captcha_answer]):
            return Response({
                'success': False,
                'error': 'Email, password, and captcha answer are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate captcha
        session_answer = request.session.get('captcha_answer')
        if not session_answer:
            return Response({
                'success': False,
                'error': 'Captcha expired. Please refresh and try again.'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            if int(captcha_answer) != session_answer:
                return Response({
                    'success': False,
                    'error': 'Invalid captcha answer'
                }, status=status.HTTP_400_BAD_REQUEST)
        except (ValueError, TypeError):
            return Response({
                'success': False,
                'error': 'Invalid captcha format'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Authenticate user by email
        user = authenticate(request, username=email, password=password)

        if user and user.user_type in ['superadmin', 'admin', 'user']:
            login(request, user)

            # Clear captcha from session
            request.session.pop('captcha_answer', None)
            request.session.pop('captcha_generated', None)

            # Log successful login
            logger.info(f"BCE login successful: {email}")

            return Response({
                'success': True,
                'user': {
                    'id': str(user.id),
                    'email': user.email,
                    'employee_id': user.employee_id,
                    'full_name': user.full_name,
                    'user_type': user.user_type,
                    'role_category': user.role_category,
                    'can_access_admin': user.is_admin,
                }
            })
        else:
            logger.warning(f"Failed BCE login attempt: {email}")
            return Response({
                'success': False,
                'error': 'Invalid credentials'
            }, status=status.HTTP_401_UNAUTHORIZED)

    except Exception as e:
        logger.error(f"Error in BCE login: {str(e)}")
        return Response({
            'success': False,
            'error': 'Login failed'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
def logout_view(request):
    """Logout user"""
    try:
        # Clear all session data
        request.session.flush()
        logout(request)
        return Response({
            'success': True,
            'message': 'Logged out successfully'
        })
    except Exception as e:
        logger.error(f"Error in logout: {str(e)}")
        return Response({
            'success': False,
            'error': 'Logout failed'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def check_auth(request):
    """Check if user is authenticated"""
    if request.user.is_authenticated:
        return Response({
            'authenticated': True,
            'user': {
                'id': str(request.user.id),
                'email': request.user.email,
                'employee_id': request.user.employee_id,
                'full_name': request.user.full_name,
                'user_type': request.user.user_type,
                'role_category': request.user.role_category,
                'can_access_admin': request.user.is_admin,
            }
        })
    else:
        return Response({
            'authenticated': False
        })

@api_view(['GET'])
@permission_classes([AllowAny])
@ensure_csrf_cookie
def get_csrf_token(request):
    """Get CSRF token for frontend"""
    return Response({
        'csrf_token': get_token(request)
    })
