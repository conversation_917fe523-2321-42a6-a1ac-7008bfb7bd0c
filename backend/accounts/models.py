from django.contrib.auth.models import AbstractUser
from django.db import models
from django.core.validators import RegexValidator
import uuid

class User(AbstractUser):
    """
    Custom User model with role-based access control
    """

    # User Types
    USER_TYPE_CHOICES = [
        ('superadmin', 'Super Admin'),
        ('admin', 'Admin'),
        ('user', 'User'),
        ('vendor', 'Vendor'),
    ]

    # Role Categories (for employees, clients, talent acquisition team)
    ROLE_CATEGORY_CHOICES = [
        ('employee', 'Employee'),
        ('client', 'Client'),
        ('talent_acquisition', 'Talent Acquisition Team'),
        ('vendor', 'Vendor'),
    ]

    # Core fields
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(unique=True)
    employee_id = models.CharField(
        max_length=20,
        unique=True,
        null=True,
        blank=True,
        validators=[RegexValidator(r'^[A-Z0-9]+$', 'Employee ID must contain only uppercase letters and numbers')]
    )

    # Role and permissions
    user_type = models.CharField(max_length=20, choices=USER_TYPE_CHOICES, default='user')
    role_category = models.CharField(max_length=20, choices=ROLE_CATEGORY_CHOICES, default='employee')

    # Profile information
    phone_number = models.CharField(
        max_length=15,
        blank=True,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$', 'Phone number must be valid')]
    )
    department = models.CharField(max_length=100, blank=True)
    position = models.CharField(max_length=100, blank=True)

    # Status and metadata
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='created_users')
    last_login_ip = models.GenericIPAddressField(null=True, blank=True)

    # Use email as username
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    class Meta:
        db_table = 'accounts_user'
        verbose_name = 'User'
        verbose_name_plural = 'Users'
        permissions = [
            ('can_manage_users', 'Can manage users'),
            ('can_manage_admins', 'Can manage admins'),
            ('can_view_admin_panel', 'Can view admin panel'),
            ('can_manage_vendors', 'Can manage vendors'),
        ]

    def __str__(self):
        return f"{self.email} ({self.get_user_type_display()})"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}".strip()

    @property
    def is_superadmin(self):
        return self.user_type == 'superadmin'

    @property
    def is_admin(self):
        return self.user_type in ['superadmin', 'admin']

    @property
    def is_vendor_user(self):
        return self.user_type == 'vendor'

    def can_create_admins(self):
        """Check if user can create admin users"""
        return self.user_type in ['superadmin', 'admin']

    def can_create_users(self):
        """Check if user can create regular users"""
        return self.user_type in ['superadmin', 'admin']

    def can_deactivate_user(self, target_user):
        """Check if user can deactivate another user"""
        if self.user_type == 'superadmin':
            return True
        elif self.user_type == 'admin':
            return target_user.user_type not in ['superadmin']
        return False
