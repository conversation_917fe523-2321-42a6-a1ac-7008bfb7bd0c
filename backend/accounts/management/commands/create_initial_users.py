from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction

User = get_user_model()

class Command(BaseCommand):
    help = 'Create initial users for testing: 1 Superadmin, 1 Admin, 1 User'

    def handle(self, *args, **options):
        with transaction.atomic():
            # Create Superadmin
            if not User.objects.filter(user_type='superadmin').exists():
                superadmin = User.objects.create_user(
                    email='<EMAIL>',
                    password='SuperAdmin@123',
                    first_name='Super',
                    last_name='Admin',
                    user_type='superadmin',
                    role_category='employee',
                    employee_id='SA001',
                    department='IT',
                    position='Super Administrator',
                    is_staff=True,
                    is_superuser=True,
                )
                self.stdout.write(
                    self.style.SUCCESS(f'Superadmin created: {superadmin.email} (Employee ID: {superadmin.employee_id})')
                )
            else:
                self.stdout.write(
                    self.style.WARNING('Superadmin already exists')
                )

            # Create Admin
            if not User.objects.filter(email='<EMAIL>').exists():
                admin = User.objects.create_user(
                    email='<EMAIL>',
                    password='Admin@123',
                    first_name='Admin',
                    last_name='User',
                    user_type='admin',
                    role_category='employee',
                    employee_id='AD001',
                    department='HR',
                    position='HR Administrator',
                    created_by=User.objects.filter(user_type='superadmin').first(),
                )
                self.stdout.write(
                    self.style.SUCCESS(f'Admin created: {admin.email} (Employee ID: {admin.employee_id})')
                )
            else:
                self.stdout.write(
                    self.style.WARNING('Admin already exists')
                )

            # Create Regular User
            if not User.objects.filter(email='<EMAIL>').exists():
                user = User.objects.create_user(
                    email='<EMAIL>',
                    password='User@123',
                    first_name='Regular',
                    last_name='User',
                    user_type='user',
                    role_category='employee',
                    employee_id='US001',
                    department='Operations',
                    position='Operations Specialist',
                    created_by=User.objects.filter(user_type='admin').first(),
                )
                self.stdout.write(
                    self.style.SUCCESS(f'User created: {user.email} (Employee ID: {user.employee_id})')
                )
            else:
                self.stdout.write(
                    self.style.WARNING('User already exists')
                )

            # Create Vendor User
            if not User.objects.filter(email='<EMAIL>').exists():
                vendor = User.objects.create_user(
                    email='<EMAIL>',
                    password='Vendor@123',
                    first_name='Vendor',
                    last_name='User',
                    user_type='vendor',
                    role_category='vendor',
                    department='External',
                    position='Vendor Representative',
                    created_by=User.objects.filter(user_type='admin').first(),
                )
                self.stdout.write(
                    self.style.SUCCESS(f'Vendor created: {vendor.email}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING('Vendor already exists')
                )

            self.stdout.write(
                self.style.SUCCESS('\n=== Initial Users Created Successfully ===')
            )
            self.stdout.write('Superadmin: <EMAIL> / SuperAdmin@123 (Employee ID: SA001)')
            self.stdout.write('Admin: <EMAIL> / Admin@123 (Employee ID: AD001)')
            self.stdout.write('User: <EMAIL> / User@123 (Employee ID: US001)')
            self.stdout.write('Vendor: <EMAIL> / Vendor@123')
            self.stdout.write(
                self.style.SUCCESS('===========================================')
            )
