Watching for file changes with StatReloader
Not Found: /
"GET / HTTP/1.1" 404 2628
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 2679
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 35
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 35
"OPTIONS /api/auth/bce-login/ HTTP/1.1" 200 0
BCE login successful: SA001 (<EMAIL>)
"POST /api/auth/bce-login/ HTTP/1.1" 200 232
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/core/dashboard/ HTTP/1.1" 200 489
Forbidden: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
