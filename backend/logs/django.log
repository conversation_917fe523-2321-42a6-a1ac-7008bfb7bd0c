Watching for file changes with StatReloader
Not Found: /
"GET / HTTP/1.1" 404 2628
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 2679
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 35
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 35
"OPTIONS /api/auth/bce-login/ HTTP/1.1" 200 0
BCE login successful: SA001 (<EMAIL>)
"POST /api/auth/bce-login/ HTTP/1.1" 200 232
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/core/dashboard/ HTTP/1.1" 200 489
Forbidden: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
Captcha generated: 13 - 17 = -4
Captcha generated: 17 * 17 = 289
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 37
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 37
Captcha generated: 19 + 20 = 39
Captcha generated: 12 + 17 = 29
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 37
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 37
Captcha generated: 13 + 10 = 23
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 37
Captcha generated: 19 + 9 = 28
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
Captcha generated: 14 + 5 = 19
Captcha generated: 9 * 3 = 27
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 35
"OPTIONS /api/auth/bce-login/ HTTP/1.1" 200 0
BCE login attempt with non-existent employee ID: <EMAIL>
Unauthorized: /api/auth/bce-login/
"POST /api/auth/bce-login/ HTTP/1.1" 401 47
Captcha generated: 9 + 13 = 22
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
Captcha generated: 18 + 20 = 38
Captcha generated: 5 + 6 = 11
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 37
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 35
Captcha generated: 9 - 12 = -3
Captcha generated: 12 * 1 = 12
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
"OPTIONS /api/auth/bce-login/ HTTP/1.1" 200 0
BCE login attempt with non-existent employee ID: <EMAIL>
Unauthorized: /api/auth/bce-login/
"POST /api/auth/bce-login/ HTTP/1.1" 401 47
Captcha generated: 4 + 11 = 15
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 36
BCE login successful: SA001 (<EMAIL>)
"POST /api/auth/bce-login/ HTTP/1.1" 200 232
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/core/dashboard/ HTTP/1.1" 200 489
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
Forbidden: /api/admin-controls/users/8a372f45-862b-40e3-9ddb-e988a8ebfe32/toggle-status/
"POST /api/admin-controls/users/8a372f45-862b-40e3-9ddb-e988a8ebfe32/toggle-status/ HTTP/1.1" 403 45
Forbidden: /api/admin-controls/users/8a372f45-862b-40e3-9ddb-e988a8ebfe32/toggle-status/
"POST /api/admin-controls/users/8a372f45-862b-40e3-9ddb-e988a8ebfe32/toggle-status/ HTTP/1.1" 403 45
Forbidden: /api/admin-controls/users/8a372f45-862b-40e3-9ddb-e988a8ebfe32/toggle-status/
"POST /api/admin-controls/users/8a372f45-862b-40e3-9ddb-e988a8ebfe32/toggle-status/ HTTP/1.1" 403 45
Forbidden: /api/admin-controls/users/e9404a5d-e423-4489-b268-dec55a76154d/toggle-status/
"POST /api/admin-controls/users/e9404a5d-e423-4489-b268-dec55a76154d/toggle-status/ HTTP/1.1" 403 45
Forbidden: /api/admin-controls/users/4dd0f8be-0772-49fe-b10d-a276853a790f/toggle-status/
"POST /api/admin-controls/users/4dd0f8be-0772-49fe-b10d-a276853a790f/toggle-status/ HTTP/1.1" 403 45
Forbidden: /api/admin-controls/users/4fbb801a-8d99-4885-b0bd-09a7cb82fba8/toggle-status/
"POST /api/admin-controls/users/4fbb801a-8d99-4885-b0bd-09a7cb82fba8/toggle-status/ HTTP/1.1" 403 45
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
"GET /api/admin-controls/users/?page=1&per_page=10 HTTP/1.1" 200 1790
Forbidden: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
"OPTIONS /api/auth/generate-captcha/ HTTP/1.1" 200 0
"OPTIONS /api/auth/generate-captcha/ HTTP/1.1" 200 0
Forbidden: /api/auth/generate-captcha/
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Captcha generated: 7 + 18 = 25 (Session: 8sfriamy2q5mfn8j7lzsheg7b42oq4o1)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 116
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Captcha generated: 11 + 6 = 17 (Session: 8sfriamy2q5mfn8j7lzsheg7b42oq4o1)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 116
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Captcha generated: 18 + 20 = 38 (Session: 8sfriamy2q5mfn8j7lzsheg7b42oq4o1)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 117
Error in BCE login: You cannot access body after reading from request's data stream
Internal Server Error: /api/auth/bce-login/
"POST /api/auth/bce-login/ HTTP/1.1" 500 40
Captcha generated: 14 + 19 = 33 (Session: 8sfriamy2q5mfn8j7lzsheg7b42oq4o1)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 117
Error in BCE login: You cannot access body after reading from request's data stream
Internal Server Error: /api/auth/bce-login/
"POST /api/auth/bce-login/ HTTP/1.1" 500 40
Captcha generated: 18 + 15 = 33 (Session: 8sfriamy2q5mfn8j7lzsheg7b42oq4o1)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 117
Error in BCE login: You cannot access body after reading from request's data stream
Internal Server Error: /api/auth/bce-login/
"POST /api/auth/bce-login/ HTTP/1.1" 500 40
Captcha generated: 8 * 17 = 136 (Session: 8sfriamy2q5mfn8j7lzsheg7b42oq4o1)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 116
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Captcha generated: 10 + 17 = 27 (Session: 8sfriamy2q5mfn8j7lzsheg7b42oq4o1)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 117
"OPTIONS /api/auth/vendor-login/ HTTP/1.1" 200 0
Error in vendor login: You cannot access body after reading from request's data stream
Internal Server Error: /api/auth/vendor-login/
"POST /api/auth/vendor-login/ HTTP/1.1" 500 40
Captcha generated: 14 + 10 = 24 (Session: 8sfriamy2q5mfn8j7lzsheg7b42oq4o1)
"POST /api/auth/generate-captcha/ HTTP/1.1" 200 117
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
"GET /api/auth/csrf-token/ HTTP/1.1" 200 81
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
Forbidden: /api/auth/generate-captcha/
"POST /api/auth/generate-captcha/ HTTP/1.1" 403 45
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
